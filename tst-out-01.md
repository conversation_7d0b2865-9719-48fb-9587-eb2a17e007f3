 RUNS  shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts
^C
dv@lnv:~/dev/web-dev/oa-prod$ npm test -- --testPathPattern="CleanupCoordinatorEnhanced.test.ts" --verbose

> oa-framework@1.0.0 test
> jest --testPathPattern=CleanupCoordinatorEnhanced.test.ts --verbose

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts (17.373 s, 247 MB heap size)
  CleanupCoordinatorEnhanced
    Cleanup Templates System
      ✓ should register and validate cleanup templates (18 ms)
      ✓ should validate template structure and detect issues (33 ms)
      ✓ should execute templates with dependency resolution (6 ms)
      ✓ should filter templates by criteria (4 ms)
      ✓ should track template execution metrics (6 ms)
    Advanced Dependency Resolution
      ✓ should create dependency graph without hanging (7 ms)
      ✓ should detect circular dependencies (3 ms)
      ✓ should build and analyze dependency graphs (4 ms)
      ✓ should optimize operation execution order (3 ms)
      ✓ should throw error for circular dependencies in optimization (12 ms)
      ✓ should identify bottlenecks and optimization opportunities (3 ms)
    Rollback and Recovery System
      ✓ should create and manage checkpoints (5 ms)
      ✓ should rollback to checkpoint successfully (7 ms)
      ✓ should rollback operation using most recent checkpoint (6 ms)
      ✓ should validate rollback capability (4 ms)
      ✓ should filter checkpoints by criteria (3 ms)
      ✓ should cleanup old checkpoints (3 ms)
      ✓ should handle rollback failures gracefully (2 ms)
    Modular Architecture Integration
      ✓ should properly initialize all 15 extracted modules (4 ms)
      ✓ should coordinate operations across modules with <5ms overhead (6 ms)
      ✕ should handle module-level errors without cascading failures (4 ms)
    Resilient Timing Integration
      ✓ should use resilient timing for all coordination operations (2 ms)
      ✓ should record timing metrics for coordination overhead (3 ms)
      ✕ should handle timing reliability issues gracefully (15007 ms)
    ES6+ Modernization Validation
      ✓ should execute modernized async/await patterns correctly (7 ms)
      ✕ should maintain identical error handling behavior (10 ms)
    Performance Requirements
      ✓ should maintain <5ms coordination overhead under load (6 ms)
      ✕ should handle 1000+ concurrent operations efficiently (81 ms)
    Integration and Performance
      ✓ should maintain backward compatibility with base CleanupCoordinator (3 ms)
      ✓ should handle template execution within performance requirements (3 ms)
      ✓ should handle dependency analysis within performance requirements (4 ms)
      ✓ should handle checkpoint creation within performance requirements (3 ms)
    Factory Functions
      ✓ should create enhanced cleanup coordinator via factory function (1 ms)
      ✓ should get enhanced cleanup coordinator via getter function (3 ms)
    Error Handling and Edge Cases
      ✓ should handle template execution with non-existent template (2 ms)
      ✓ should handle rollback with non-existent checkpoint (11 ms)
      ✓ should handle rollback with no checkpoints for operation (2 ms)
      ✓ should handle disabled rollback system (4 ms)
      ✓ should handle empty template operations (2 ms)
      ✓ should handle malformed component patterns (2 ms)

  ● CleanupCoordinatorEnhanced › Modular Architecture Integration › should handle module-level errors without cascading failures

    expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      1100 |         testMode: (coordinator as any)._config.testMode
      1101 |       });
    > 1102 |       expect(healthStatus.operational).toBe(true);
           |                                        ^
      1103 |
      1104 |       // Verify other modules are still functional
      1105 |       const moduleStatus = await coordinator.getModuleStatus();

      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:1102:40)

  ● CleanupCoordinatorEnhanced › Resilient Timing Integration › should handle timing reliability issues gracefully

    thrown: "Exceeded timeout of 15000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      1196 |     });
      1197 |
    > 1198 |     it('should handle timing reliability issues gracefully', async () => {
           |     ^
      1199 |       // Reliability Test: Fallback mechanisms
      1200 |
      1201 |       // Simulate timing reliability issues by mocking unstable timing

      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:1198:5
      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:1114:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:34:1)

  ● CleanupCoordinatorEnhanced › ES6+ Modernization Validation › should maintain identical error handling behavior

    expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      1319 |         testMode: (coordinator as any)._config.testMode
      1320 |       });
    > 1321 |       expect(healthStatus.operational).toBe(true);
           |                                        ^
      1322 |     });
      1323 |   });
      1324 |

      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:1321:40)

  ● CleanupCoordinatorEnhanced › Performance Requirements › should handle 1000+ concurrent operations efficiently

    expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      1419 |       console.log('Operations completed:', results.filter(r => r.success).length);
      1420 |
    > 1421 |       expect(healthStatus.operational).toBe(true);
           |                                        ^
      1422 |       expect(healthStatus.memoryUsage).toBeLessThan(100 * 1024 * 1024); // Under 100MB
      1423 |     }, 30000); // 30 second timeout for scalability test
      1424 |   });

      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:1421:40)

Test Suites: 1 failed, 1 total
Tests:       4 failed, 36 passed, 40 total
Snapshots:   0 total
Time:        17.643 s
Ran all test suites matching /CleanupCoordinatorEnhanced.test.ts/i.
